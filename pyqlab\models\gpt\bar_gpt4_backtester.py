"""
BarGpt4Backtester - 用于回测BarGpt4模型的回测器

此模块提供了一个专门用于BarGpt4模型的回测器，支持不同的信号生成策略和风险管理功能。
"""

import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, List, Tuple, Union, Optional, Any
from datetime import datetime
import os
import json
import inspect
from abc import ABC, abstractmethod
from pyqlab.data.dataset.utils import idx2token
from pyqlab.data.dataset.utils import candlestick_to_bar_token
import talib

# 信号生成策略配置
signal_configs = {
    "threshold": {
        "name": "阈值策略",
        "description": "基于预测概率阈值生成交易信号"
    },
    "topk": {
        "name": "TopK策略",
        "description": "选择概率最高的K个预测生成交易信号"
    },
    "momentum": {
        "name": "动量策略",
        "description": "基于预测的统计动量生成交易信号"
    },
    "ensemble": {
        "name": "集成策略",
        "description": "组合多种策略生成交易信号"
    }
}

class SignalStrategy(ABC):
    """信号生成策略基类"""

    @abstractmethod
    def generate_signal(self, logits: torch.Tensor, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        """
        生成交易信号

        Args:
            logits: 模型输出的logits
            temperature: 温度参数，控制预测的随机性
            **kwargs: 其他参数

        Returns:
            包含信号信息的字典
        """
        pass

class ThresholdSignalStrategy(SignalStrategy):
    """基于阈值的信号生成策略"""

    def __init__(self, threshold: float = 0.6):
        """
        初始化阈值策略

        Args:
            threshold: 信号阈值，超过此值生成交易信号
        """
        self.threshold = threshold

    def generate_signal(self, logits: torch.Tensor, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        """
        基于阈值生成交易信号

        Args:
            logits: 模型输出的logits
            temperature: 温度参数，控制预测的随机性
            **kwargs: 其他参数

        Returns:
            包含信号信息的字典
        """
        # 获取最后一个时间步的logits
        last_logits = logits[:, -1, :] / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()

        # 获取最高概率及其索引
        max_prob = probs.max()
        max_idx = probs.argmax()

        # 根据阈值生成信号
        if max_prob > self.threshold:
            try:
                # 确保idx是标量
                idx_scalar = max_idx.item() if hasattr(max_idx, 'item') else int(max_idx)
                token_str = idx2token(idx_scalar)

                # 解析token字符串，获取change值
                # token格式: change|entity|upline|downline
                change_str = token_str.split('|')[0]

                # 特殊标记处理
                if change_str in ['-88', '-99', '-77']:
                    signal = {'signal': 'HOLD', 'confidence': float(max_prob)}
                else:
                    change = int(change_str)

                    # 根据change值判断涨跌
                    if change > 1:
                        signal = {'signal': 'BUY', 'confidence': float(max_prob)}
                    elif change < -1:
                        signal = {'signal': 'SELL', 'confidence': float(max_prob)}
                    else:
                        signal = {'signal': 'HOLD', 'confidence': float(max_prob)}
            except Exception as e:
                print(f"Error parsing token {max_idx}: {str(e)}")
                signal = {'signal': 'HOLD', 'confidence': float(max_prob)}
        else:
            signal = {'signal': 'HOLD', 'confidence': float(max_prob)}

        # 添加额外信息
        signal['max_prob'] = float(max_prob)
        signal['max_idx'] = int(max_idx)

        return signal

class TopKSignalStrategy(SignalStrategy):
    """基于TopK的信号生成策略"""

    def __init__(self, k: int = 5, min_prob: float = 0.3):
        """
        初始化TopK策略

        Args:
            k: 考虑的最高概率数量
            min_prob: 最小概率阈值
        """
        self.k = k
        self.min_prob = min_prob

    def generate_signal(self, logits: torch.Tensor, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        """
        基于TopK生成交易信号

        Args:
            logits: 模型输出的logits
            temperature: 温度参数，控制预测的随机性
            **kwargs: 其他参数

        Returns:
            包含信号信息的字典
        """
        # 获取最后一个时间步的logits
        last_logits = logits[:, -1, :] / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()

        # 获取TopK概率及其索引
        top_probs, top_indices = torch.topk(torch.tensor(probs), min(self.k, probs.shape[-1]))
        top_probs = top_probs.numpy()
        top_indices = top_indices.numpy()
        # top_indices to token
        # 确保我们正确处理二维数组
        if len(top_indices.shape) > 1:
            top_indices_flat = top_indices[0]
        else:
            top_indices_flat = top_indices

        top_tokens = []
        for idx in top_indices_flat:
            try:
                # 确保idx是标量
                idx_scalar = idx.item() if hasattr(idx, 'item') else int(idx)
                top_tokens.append(idx2token(idx_scalar))
            except Exception as e:
                print(f"Error converting index {idx} to token: {str(e)}")
                top_tokens.append(f"ERROR_{idx}")

        # print(f"top_probs: {top_probs}")
        # print(f"top_indices: {top_tokens}")

        # 确保我们正确处理二维数组
        # 如果是二维数组，取第一行
        if len(top_probs.shape) > 1:
            top_probs_flat = top_probs[0]
            top_indices_flat = top_indices[0]
        else:
            top_probs_flat = top_probs
            top_indices_flat = top_indices

        # 解析token中的change值
        up_prob = 0.0
        down_prob = 0.0

        for prob, idx in zip(top_probs_flat, top_indices_flat):
            try:
                # 确保idx是标量
                idx_scalar = idx.item() if hasattr(idx, 'item') else int(idx)
                token_str = idx2token(idx_scalar)

                # 解析token字符串，获取change值
                # token格式: change|entity|upline|downline
                change_str = token_str.split('|')[0]

                # 特殊标记处理
                if change_str in ['-88', '-99', '-77']:
                    continue

                change = int(change_str)

                # 根据change值判断涨跌
                if change > 0:
                    up_prob += prob
                elif change < 0:
                    down_prob += prob
            except Exception as e:
                print(f"Error parsing token {idx}: {str(e)}")
                continue

        # 根据TopK概率生成信号
        if up_prob > self.min_prob and up_prob > down_prob:
            signal = {'signal': 'BUY', 'confidence': float(up_prob)}
        elif down_prob > self.min_prob and down_prob > up_prob:
            signal = {'signal': 'SELL', 'confidence': float(down_prob)}
        else:
            signal = {'signal': 'HOLD', 'confidence': max(float(up_prob), float(down_prob))}

        # 添加额外信息
        signal['up_prob'] = float(up_prob)
        signal['down_prob'] = float(down_prob)
        signal['top_indices'] = top_indices_flat.tolist()
        signal['top_probs'] = top_probs_flat.tolist()

        return signal

class MomentumSignalStrategy(SignalStrategy):
    """基于动量的信号生成策略"""

    def __init__(self, momentum_threshold: float = 0.2):
        """
        初始化动量策略

        Args:
            momentum_threshold: 动量阈值
        """
        self.momentum_threshold = momentum_threshold

    def generate_signal(self, logits: torch.Tensor, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        """
        基于动量生成交易信号

        Args:
            logits: 模型输出的logits
            temperature: 温度参数，控制预测的随机性
            **kwargs: 其他参数

        Returns:
            包含信号信息的字典
        """
        # 获取最后一个时间步的logits
        last_logits = logits[:, -1, :] / temperature

        # 计算概率分布
        probs = F.softmax(last_logits, dim=-1).cpu().numpy()

        # 计算上涨和下跌的动量
        up_momentum = 0.0
        down_momentum = 0.0

        # 遍历所有可能的token
        for i in range(logits.shape[-1]):
            try:
                # 获取token字符串
                token_str = idx2token(i)

                # 解析token字符串，获取change值
                change_str = token_str.split('|')[0]

                # 特殊标记处理
                if change_str in ['-88', '-99', '-77']:
                    continue

                change = int(change_str)

                # 根据change值计算动量
                if change > 0:
                    # 上涨动量，change越大，动量越大
                    up_momentum += probs[0, i] * abs(change) / 12  # 归一化，假设最大change为12
                elif change < 0:
                    # 下跌动量，change越小（负值越大），动量越大
                    down_momentum += probs[0, i] * abs(change) / 12  # 归一化，假设最小change为-12
            except Exception as e:
                print(f"Error parsing token {i}: {str(e)}")
                continue

        # 根据动量生成信号
        if up_momentum > self.momentum_threshold and up_momentum > down_momentum:
            signal = {'signal': 'BUY', 'confidence': min(1.0, float(up_momentum))}
        elif down_momentum > self.momentum_threshold and down_momentum > up_momentum:
            signal = {'signal': 'SELL', 'confidence': min(1.0, float(down_momentum))}
        else:
            signal = {'signal': 'HOLD', 'confidence': max(float(up_momentum), float(down_momentum))}

        # 添加额外信息
        signal['up_momentum'] = float(up_momentum)
        signal['down_momentum'] = float(down_momentum)

        return signal

class EnsembleSignalStrategy(SignalStrategy):
    """集成多种信号生成策略"""

    def __init__(self, strategies: List[Tuple[SignalStrategy, float]]):
        """
        初始化集成策略

        Args:
            strategies: 策略列表，每个元素为(策略实例, 权重)
        """
        self.strategies = strategies

        # 归一化权重
        total_weight = sum(weight for _, weight in strategies)
        self.normalized_weights = [weight / total_weight for _, weight in strategies]

    def generate_signal(self, logits: torch.Tensor, temperature: float = 1.0, **kwargs) -> Dict[str, Any]:
        """
        集成多种策略生成交易信号

        Args:
            logits: 模型输出的logits
            temperature: 温度参数，控制预测的随机性
            **kwargs: 其他参数

        Returns:
            包含信号信息的字典
        """
        # 收集各策略的信号
        signals = []
        for (strategy, _), weight in zip(self.strategies, self.normalized_weights):
            signal = strategy.generate_signal(logits, temperature, **kwargs)
            signals.append((signal, weight))

        # 计算加权信号
        buy_confidence = sum(signal['confidence'] * weight for signal, weight in signals
                            if signal['signal'] == 'BUY')
        sell_confidence = sum(signal['confidence'] * weight for signal, weight in signals
                             if signal['signal'] == 'SELL')
        hold_confidence = sum(signal['confidence'] * weight for signal, weight in signals
                             if signal['signal'] == 'HOLD')

        # 确定最终信号
        if buy_confidence > sell_confidence and buy_confidence > hold_confidence:
            final_signal = {'signal': 'BUY', 'confidence': buy_confidence}
        elif sell_confidence > buy_confidence and sell_confidence > hold_confidence:
            final_signal = {'signal': 'SELL', 'confidence': sell_confidence}
        else:
            final_signal = {'signal': 'HOLD', 'confidence': hold_confidence}

        # 添加各策略的信号
        final_signal['strategy_signals'] = [signal for signal, _ in signals]
        final_signal['strategy_weights'] = self.normalized_weights

        return final_signal

class BarGpt4Backtester:
    """BarGpt4模型回测器"""
    def __init__(self, model, initial_capital=10000.0, device=None, signal_type=None):
        """
        初始化回测器

        Args:
            model: BarGpt4模型
            initial_capital: 初始资金
            device: 计算设备 (None表示自动选择)
            signal_type: 信号生成器类型 (None表示使用传统策略)
        """
        self.model = model
        self.initial_capital = initial_capital
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')

        # 将模型移动到指定设备
        self.model.to(self.device)

        # 设置模型为推理模式
        self.model.inference_mode()

        # 初始化信号生成器
        self.signal_generator = None
        if signal_type is not None:
            if signal_type in signal_configs:
                print(f"使用信号生成策略: {signal_configs[signal_type]['name']}")
                self.signal_generator = self._create_signal_generator(signal_type)
            else:
                print(f"警告: 未知的信号类型 '{signal_type}'，使用默认策略")
                self.signal_generator = self._create_signal_generator("threshold")
        else:
            # 默认使用阈值策略
            self.signal_generator = self._create_signal_generator("threshold")

        # 存储最近的预测
        self.recent_predictions = {}

    def backtest(self, df, seq_len=30, commission=0.001, threshold=0.6,
                stop_loss=None, take_profit=None, temperature=1.0, print_interval=10):
        """
        回测模型

        Args:
            df: 包含OHLCV数据的DataFrame
            seq_len: 序列长度
            commission: 交易手续费率
            threshold: 交易信号阈值 (用于创建默认信号生成器)
            stop_loss: 止损比例 (None表示不使用止损)
            take_profit: 止盈比例 (None表示不使用止盈)
            temperature: 温度参数，控制预测的随机性 (1.0表示正常温度，<1.0更确定，>1.0更随机)
            print_interval: 打印详细信息的间隔步数

        Returns:
            包含回测结果的字典
        """
        df.reset_index(inplace=True)
        # 检查数据中是否包含必要的列
        required_columns = ['open', 'high', 'low', 'close', 'datetime']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"数据缺少必要的列: {col}")

        # 计算ATR
        atr_window = 100
        if len(df) >= atr_window:
            atr_values = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=atr_window)
            atr = atr_values[-1]  # 使用最后一个ATR值
            # 处理NaN值
            if pd.isna(atr):
                atr = df['close'].std() * 0.1
        else:
            # 如果数据不足，使用简化的ATR计算
            atr = df['close'].std() * 0.1

        # 获取前一个收盘价
        pre_close = df['close'].iloc[0] * 0.99  # 使用第一个收盘价的99%作为前一个收盘价

        # 获取证券代码
        if 'code' in df.columns:
            base_code = df['code'].iloc[0]
            code = base_code[:-7] if len(base_code) > 7 else base_code

        # 重新排列列的顺序以匹配candlestick_to_bar_token的期望格式
        # 期望格式: datetime, open, high, low, close
        df_for_token = df[['datetime', 'open', 'high', 'low', 'close']].copy()

        # 使用candlestick_to_bar_token生成token
        code_series, x_series, x_mark_series = candlestick_to_bar_token(
            code, df_for_token, pre_close, atr, timeenc=2, atr_mult=0.88, scale=10
        )
        print(f"df_for_token.shape: {df_for_token.shape}")
        print(f"code_id: {code_series.shape}")
        print(f"x.shape: {x_series.shape}")
        print(f"x_mark.shape: {x_mark_series.shape}")

        # 确保模型处于评估模式
        self.model.eval()
        self.model.to(self.device)

        # 初始化回测状态
        capital = self.initial_capital
        position = 0
        entry_price = 0
        trades = []
        equity_curve = [capital]
        daily_returns = []
        positions_history = []
        signals = []

        # 准备回测数据
        total_steps = len(x_series) - seq_len
        print(f"开始回测，共 {total_steps} 个时间步...")

        # 遍历每个时间步
        for i in range(total_steps):
            # 准备模型输入
            code = torch.tensor(code_series[i: i+seq_len], dtype=torch.long).to(self.device)
            x = torch.tensor(x_series[i: i+seq_len], dtype=torch.long).to(self.device)
            x_mark = torch.tensor(x_mark_series[i: i+seq_len], dtype=torch.long).to(self.device)

            code = code.reshape(1, -1)
            x = x.reshape(1, -1)
            x_mark = x_mark.reshape(1, -1, 5)

            current_datetime = df.iloc[i+seq_len-1].name if isinstance(df.iloc[i+seq_len-1].name, pd.Timestamp) else pd.Timestamp(df.iloc[i+seq_len-1].name)
            current_price = df.iloc[i+seq_len-1]['close']

            # 生成预测
            with torch.no_grad():
                logits, _ = self.model(code, x, x_mark)

            # 计算当前收益率（如果有持仓）
            current_return = 0
            if position != 0:
                current_return = (current_price - entry_price) / entry_price * position

            # 检查止损和止盈条件
            if position != 0:
                # 止损
                if stop_loss is not None and current_return < -stop_loss:
                    # 平仓
                    capital = capital + position * current_price * (1 - commission)
                    trades.append({
                        'datetime': current_datetime,
                        'action': 'STOP_LOSS',
                        'price': current_price,
                        'position': 0,
                        'capital': capital,
                        'equity': capital,
                        'profit': (current_price - entry_price) * position - commission * (entry_price + current_price) * abs(position)
                    })
                    position = 0

                # 止盈
                elif take_profit is not None and current_return > take_profit:
                    # 平仓
                    capital = capital + position * current_price * (1 - commission)
                    trades.append({
                        'datetime': current_datetime,
                        'action': 'TAKE_PROFIT',
                        'price': current_price,
                        'position': 0,
                        'capital': capital,
                        'equity': capital,
                        'profit': (current_price - entry_price) * position - commission * (entry_price + current_price) * abs(position)
                    })
                    position = 0

            # 生成交易信号
            signal = self.signal_generator.generate_signal(
                logits=logits,
                temperature=temperature
            )
            signals.append(signal)

            # 执行交易
            if signal['signal'] == 'BUY' and position <= 0:
                # 如果当前没有持仓或者持有空仓，买入
                old_position = position
                position = 1

                # 计算交易成本（仅用于记录）
                # trade_cost = current_price * (1 + commission)

                # 如果之前有空仓，先平仓
                if old_position < 0:
                    # 平空仓
                    profit = (entry_price - current_price) * abs(old_position) - commission * (entry_price + current_price) * abs(old_position)
                    capital = capital + profit

                # 买入
                entry_price = current_price
                capital = capital - current_price * (1 + commission)

                trades.append({
                    'datetime': current_datetime,
                    'action': 'BUY',
                    'price': current_price,
                    'position': position,
                    'capital': capital,
                    'equity': capital + position * current_price,
                    'profit': 0
                })

            elif signal['signal'] == 'SELL' and position >= 0:
                # 如果当前没有持仓或者持有多仓，卖出
                old_position = position
                position = -1

                # 计算交易成本（仅用于记录）
                # trade_cost = current_price * (1 + commission)

                # 如果之前有多仓，先平仓
                if old_position > 0:
                    # 平多仓
                    profit = (current_price - entry_price) * old_position - commission * (entry_price + current_price) * old_position
                    capital = capital + profit

                # 卖出
                entry_price = current_price
                capital = capital + current_price * (1 - commission)

                trades.append({
                    'datetime': current_datetime,
                    'action': 'SELL',
                    'price': current_price,
                    'position': position,
                    'capital': capital,
                    'equity': capital + position * current_price,
                    'profit': 0
                })

            # 更新权益曲线
            equity = capital
            if position != 0:
                equity = capital + position * current_price
            equity_curve.append(equity)

            # 记录持仓历史
            positions_history.append(position)

            # 计算每日收益率
            if i > 0:
                daily_return = (equity_curve[-1] - equity_curve[-2]) / equity_curve[-2]
                daily_returns.append(daily_return)

            # 打印进度
            if i % print_interval == 0 or i == total_steps - 1:
                print(f"步骤 {i+1}/{total_steps} - 日期: {current_datetime} - 价格: {current_price:.2f} - 信号: {signal['signal']} - 持仓: {position} - 权益: {equity:.2f}")

        # 计算回测结果
        total_return = (equity_curve[-1] - self.initial_capital) / self.initial_capital
        annual_return = self._calculate_annual_return(equity_curve, df.index)
        sharpe_ratio = self._calculate_sharpe_ratio(daily_returns)
        max_drawdown = self._calculate_max_drawdown(equity_curve)
        win_rate, profit_loss_ratio = self._calculate_trade_stats(trades)

        # 汇总结果
        results = {
            'initial_capital': self.initial_capital,
            'final_equity': equity_curve[-1],
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'trades': trades,
            'equity_curve': equity_curve,
            'positions_history': positions_history,
            'signals': signals
        }

        # 打印回测结果摘要
        print("\n回测结果摘要:")
        print(f"初始资金: {self.initial_capital:.2f}")
        print(f"最终权益: {equity_curve[-1]:.2f}")
        print(f"总收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"胜率: {win_rate:.2%}")
        print(f"盈亏比: {profit_loss_ratio:.2f}")
        print(f"交易次数: {len(trades)}")

        return results

    def _create_time_features(self, dates):
        """
        创建时间特征

        Args:
            dates: 日期时间序列

        Returns:
            时间特征数组
        """
        # 确保dates是DatetimeIndex
        if not isinstance(dates, pd.DatetimeIndex):
            if isinstance(dates, pd.Series):
                dates = pd.DatetimeIndex(dates)
            else:
                dates = pd.to_datetime(dates)

        # 创建基本时间特征
        month = dates.month.values / 12.0 - 0.5
        day = dates.day.values / 31.0 - 0.5
        weekday = dates.weekday.values / 6.0 - 0.5
        hour = dates.hour.values / 23.0 - 0.5
        minute = dates.minute.values / 59.0 - 0.5

        # 组合特征
        features = np.column_stack([month, day, weekday, hour, minute])
        return features.reshape(1, -1, 5)  # 添加批次维度

    def _calculate_annual_return(self, equity_curve, dates):
        """
        计算年化收益率

        Args:
            equity_curve: 权益曲线
            dates: 日期序列

        Returns:
            年化收益率
        """
        if len(equity_curve) <= 1:
            return 0.0

        # 计算回测天数
        if isinstance(dates, pd.DatetimeIndex):
            start_date = dates[0]
            end_date = dates[-1]
        else:
            start_date = pd.to_datetime(dates[0])
            end_date = pd.to_datetime(dates[-1])

        days = (end_date - start_date).days
        if days <= 0:
            days = 1

        # 计算年化收益率
        total_return = (equity_curve[-1] - equity_curve[0]) / equity_curve[0]
        annual_return = (1 + total_return) ** (365 / days) - 1

        return annual_return

    def _calculate_sharpe_ratio(self, daily_returns, risk_free_rate=0.0):
        """
        计算夏普比率

        Args:
            daily_returns: 每日收益率列表
            risk_free_rate: 无风险利率

        Returns:
            夏普比率
        """
        if len(daily_returns) <= 1:
            return 0.0

        # 计算超额收益率
        excess_returns = np.array(daily_returns) - risk_free_rate / 252

        # 计算夏普比率
        sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)

        return sharpe_ratio

    def _calculate_max_drawdown(self, equity_curve):
        """
        计算最大回撤

        Args:
            equity_curve: 权益曲线

        Returns:
            最大回撤
        """
        if len(equity_curve) <= 1:
            return 0.0

        # 计算累计最大值
        running_max = np.maximum.accumulate(equity_curve)

        # 计算回撤
        drawdown = (running_max - equity_curve) / running_max

        # 计算最大回撤
        max_drawdown = np.max(drawdown)

        return max_drawdown

    def _calculate_trade_stats(self, trades):
        """
        计算交易统计数据

        Args:
            trades: 交易列表

        Returns:
            胜率和盈亏比
        """
        if len(trades) <= 1:
            return 0.0, 0.0

        # 计算盈利和亏损交易
        profits = [trade['profit'] for trade in trades if trade['profit'] > 0]
        losses = [trade['profit'] for trade in trades if trade['profit'] < 0]

        # 计算胜率
        win_rate = len(profits) / len(trades) if len(trades) > 0 else 0.0

        # 计算盈亏比
        avg_profit = np.mean(profits) if len(profits) > 0 else 0.0
        avg_loss = np.mean(losses) if len(losses) > 0 else 0.0
        profit_loss_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else 0.0

        return win_rate, profit_loss_ratio

    def visualize_backtest(self, df, results, seq_len=30, save_path=None):
        """
        可视化回测结果

        Args:
            df: 原始数据DataFrame
            results: 回测结果字典
            seq_len: 序列长度
            save_path: 保存图表的路径 (可选)

        Returns:
            matplotlib图表对象
        """
        # 创建图表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 15), gridspec_kw={'height_ratios': [3, 1, 1]})

        # 获取日期
        if isinstance(df.index, pd.DatetimeIndex):
            dates = df.index[seq_len:]
        else:
            dates = pd.to_datetime(df.index[seq_len:])

        # 绘制价格和交易
        ax1.plot(dates, df['close'].values[seq_len:], label='价格', color='blue', alpha=0.6)

        # 标记交易点
        buy_dates = [trade['datetime'] for trade in results['trades'] if trade['action'] == 'BUY']
        buy_prices = [trade['price'] for trade in results['trades'] if trade['action'] == 'BUY']
        sell_dates = [trade['datetime'] for trade in results['trades'] if trade['action'] == 'SELL']
        sell_prices = [trade['price'] for trade in results['trades'] if trade['action'] == 'SELL']
        stop_loss_dates = [trade['datetime'] for trade in results['trades'] if trade['action'] == 'STOP_LOSS']
        stop_loss_prices = [trade['price'] for trade in results['trades'] if trade['action'] == 'STOP_LOSS']
        take_profit_dates = [trade['datetime'] for trade in results['trades'] if trade['action'] == 'TAKE_PROFIT']
        take_profit_prices = [trade['price'] for trade in results['trades'] if trade['action'] == 'TAKE_PROFIT']

        ax1.scatter(buy_dates, buy_prices, marker='^', color='green', s=100, label='买入')
        ax1.scatter(sell_dates, sell_prices, marker='v', color='red', s=100, label='卖出')
        ax1.scatter(stop_loss_dates, stop_loss_prices, marker='x', color='purple', s=100, label='止损')
        ax1.scatter(take_profit_dates, take_profit_prices, marker='*', color='orange', s=100, label='止盈')

        # 设置标题和标签
        ax1.set_title('价格和交易信号')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True)

        # 格式化日期轴
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.AutoDateLocator())

        # 绘制权益曲线
        ax2.plot(dates, results['equity_curve'][1:], label='权益曲线', color='green')
        ax2.set_title('权益曲线')
        ax2.set_ylabel('权益')
        ax2.grid(True)

        # 绘制持仓
        ax3.plot(dates, results['positions_history'], label='持仓', color='orange')
        ax3.set_title('持仓历史')
        ax3.set_ylabel('持仓')
        ax3.set_ylim([-1.5, 1.5])
        ax3.grid(True)

        # 设置x轴标签
        ax3.set_xlabel('日期')

        # 调整布局
        plt.tight_layout()

        # 保存图表
        if save_path:
            plt.savefig(save_path)
            print(f"图表已保存到 {save_path}")

        return fig

    def save_results(self, results, save_path):
        """
        保存回测结果

        Args:
            results: 回测结果字典
            save_path: 保存路径

        Returns:
            是否保存成功
        """
        try:
            # 转换不可序列化的对象
            serializable_results = results.copy()

            # 转换日期时间
            if 'trades' in serializable_results:
                for trade in serializable_results['trades']:
                    if isinstance(trade['datetime'], pd.Timestamp):
                        trade['datetime'] = trade['datetime'].isoformat()

            # 转换numpy数组
            for key, value in serializable_results.items():
                if isinstance(value, np.ndarray):
                    serializable_results[key] = value.tolist()
                elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], np.ndarray):
                    serializable_results[key] = [v.tolist() for v in value]

            # 保存为JSON
            with open(save_path, 'w') as f:
                json.dump(serializable_results, f, indent=4)

            print(f"回测结果已保存到 {save_path}")
            return True
        except Exception as e:
            print(f"保存回测结果时出错: {str(e)}")
            return False

    def load_results(self, load_path):
        """
        加载回测结果

        Args:
            load_path: 加载路径

        Returns:
            回测结果字典
        """
        try:
            with open(load_path, 'r') as f:
                results = json.load(f)

            # 转换日期时间
            if 'trades' in results:
                for trade in results['trades']:
                    if isinstance(trade['datetime'], str):
                        trade['datetime'] = pd.to_datetime(trade['datetime'])

            print(f"回测结果已从 {load_path} 加载")
            return results
        except Exception as e:
            print(f"加载回测结果时出错: {str(e)}")
            return None

    def backtest_multiple(self, data_dict, seq_len=30, commission=0.001, threshold=0.6,
                         stop_loss=None, take_profit=None, temperature=1.0, print_interval=10,
                         allocation=None):
        """
        对多个证券代码进行回测

        Args:
            data_dict: 包含多个证券代码数据的字典，格式为 {code_id: dataframe}
            seq_len: 序列长度
            commission: 交易手续费率
            threshold: 交易信号阈值
            stop_loss: 止损比例 (None表示不使用止损)
            take_profit: 止盈比例 (None表示不使用止盈)
            temperature: 温度参数，控制预测的随机性
            print_interval: 打印详细信息的间隔步数
            allocation: 资金分配比例字典，格式为 {code_id: allocation_ratio}，如果为None则平均分配

        Returns:
            包含所有回测结果的字典，格式为 {code_id: results}
        """
        # 检查输入
        if not data_dict:
            raise ValueError("数据字典不能为空")

        # 如果没有指定资金分配比例，则平均分配
        if allocation is None:
            allocation = {code_id: 1.0 / len(data_dict) for code_id in data_dict.keys()}
        else:
            # 确保所有代码都有分配比例
            for code_id in data_dict.keys():
                if code_id not in allocation:
                    allocation[code_id] = 0.0

            # 归一化分配比例
            total_allocation = sum(allocation.values())
            if total_allocation > 0:
                allocation = {code_id: ratio / total_allocation for code_id, ratio in allocation.items()}
            else:
                allocation = {code_id: 1.0 / len(data_dict) for code_id in data_dict.keys()}

        # 存储所有回测结果
        all_results = {}
        total_equity = 0.0
        combined_trades = []
        combined_equity_curve = [self.initial_capital]
        combined_positions = {}

        # 对每个证券代码进行回测
        print(f"开始对 {len(data_dict)} 个证券代码进行回测...")

        for code_id, df in data_dict.items():
            print(f"\n回测证券代码 {code_id}...")

            # 计算分配给该证券的初始资金
            initial_capital = self.initial_capital * allocation[code_id]

            # 保存原始初始资金
            original_capital = self.initial_capital
            self.initial_capital = initial_capital

            # 进行回测
            results = self.backtest(
                df=df,
                seq_len=seq_len,
                commission=commission,
                threshold=threshold,
                stop_loss=stop_loss,
                take_profit=take_profit,
                temperature=temperature,
                print_interval=print_interval
            )

            # 恢复原始初始资金
            self.initial_capital = original_capital

            # 存储结果
            all_results[code_id] = results

            # 累加最终权益
            total_equity += results['final_equity']

            # 添加代码标识到交易记录
            for trade in results['trades']:
                trade['code_id'] = code_id
                combined_trades.append(trade)

            # 记录持仓历史
            combined_positions[code_id] = results['positions_history']

        # 对交易按时间排序
        combined_trades.sort(key=lambda x: x['datetime'])

        # 计算组合权益曲线
        # 注意：这是一个简化的方法，假设所有证券的回测时间段相同
        if all_results:
            # 找出最长的权益曲线
            max_length = max(len(results['equity_curve']) for results in all_results.values())

            # 初始化组合权益曲线
            combined_equity_curve = [0.0] * max_length
            combined_equity_curve[0] = self.initial_capital

            # 累加各证券的权益曲线
            for code_id, results in all_results.items():
                equity_curve = results['equity_curve']
                for i in range(1, len(equity_curve)):
                    if i < max_length:
                        # 计算相对于初始资金的收益
                        relative_return = (equity_curve[i] - equity_curve[0]) / equity_curve[0]
                        # 将收益应用到组合权益曲线
                        combined_equity_curve[i] += self.initial_capital * allocation[code_id] * (1 + relative_return)

        # 计算组合回测结果
        combined_results = {
            'initial_capital': self.initial_capital,
            'final_equity': total_equity,
            'total_return': (total_equity - self.initial_capital) / self.initial_capital,
            'trades': combined_trades,
            'equity_curve': combined_equity_curve,
            'positions': combined_positions,
            'individual_results': all_results
        }

        # 打印组合回测结果摘要
        print("\n组合回测结果摘要:")
        print(f"初始资金: {self.initial_capital:.2f}")
        print(f"最终权益: {total_equity:.2f}")
        print(f"总收益率: {combined_results['total_return']:.2%}")
        print(f"交易次数: {len(combined_trades)}")

        return combined_results

    def visualize_multiple_backtest(self, data_dict, results, seq_len=30, save_path=None):
        """
        可视化多个证券代码的回测结果

        Args:
            data_dict: 包含多个证券代码数据的字典，格式为 {code_id: dataframe}
            results: 多个证券代码的回测结果
            seq_len: 序列长度
            save_path: 保存图表的路径 (可选)

        Returns:
            matplotlib图表对象
        """
        # 创建图表
        n_codes = len(data_dict)
        fig, axes = plt.subplots(n_codes + 1, 2, figsize=(20, 5 * (n_codes + 1)))

        # 绘制组合权益曲线
        ax_combined = axes[0, 0]
        ax_combined.plot(results['equity_curve'], label='组合权益曲线', color='blue')
        ax_combined.set_title('组合权益曲线')
        ax_combined.set_ylabel('权益')
        ax_combined.grid(True)
        ax_combined.legend()

        # 绘制组合收益率分布
        ax_returns = axes[0, 1]
        if len(results['equity_curve']) > 1:
            returns = np.diff(results['equity_curve']) / results['equity_curve'][:-1]
            ax_returns.hist(returns, bins=50, alpha=0.75, color='blue')
            ax_returns.set_title('组合日收益率分布')
            ax_returns.set_xlabel('日收益率')
            ax_returns.set_ylabel('频率')
            ax_returns.grid(True)

        # 绘制各个证券的回测结果
        for i, (code_id, df) in enumerate(data_dict.items(), 1):
            individual_results = results['individual_results'][code_id]

            # 获取日期
            if isinstance(df.index, pd.DatetimeIndex):
                dates = df.index[seq_len:]
            else:
                dates = pd.to_datetime(df.index[seq_len:])

            # 绘制价格和交易
            ax_price = axes[i, 0]
            ax_price.plot(dates, df['close'].values[seq_len:], label=f'代码 {code_id} 价格', color='blue', alpha=0.6)

            # 标记交易点
            for trade in results['trades']:
                if trade['code_id'] == code_id:
                    if trade['action'] == 'BUY':
                        ax_price.scatter(trade['datetime'], trade['price'], marker='^', color='green', s=100)
                    elif trade['action'] == 'SELL':
                        ax_price.scatter(trade['datetime'], trade['price'], marker='v', color='red', s=100)
                    elif trade['action'] == 'STOP_LOSS':
                        ax_price.scatter(trade['datetime'], trade['price'], marker='x', color='purple', s=100)
                    elif trade['action'] == 'TAKE_PROFIT':
                        ax_price.scatter(trade['datetime'], trade['price'], marker='*', color='orange', s=100)

            ax_price.set_title(f'代码 {code_id} 价格和交易信号')
            ax_price.set_ylabel('价格')
            ax_price.grid(True)

            # 格式化日期轴
            ax_price.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax_price.xaxis.set_major_locator(mdates.AutoDateLocator())

            # 绘制权益曲线
            ax_equity = axes[i, 1]
            ax_equity.plot(dates, individual_results['equity_curve'][1:], label=f'代码 {code_id} 权益曲线', color='green')
            ax_equity.set_title(f'代码 {code_id} 权益曲线')
            ax_equity.set_ylabel('权益')
            ax_equity.grid(True)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        if save_path:
            plt.savefig(save_path)
            print(f"多代码回测图表已保存到 {save_path}")

        return fig

    def compare_strategies(self, results_list, names=None, save_path=None):
        """
        比较多个回测策略的结果

        Args:
            results_list: 回测结果字典列表
            names: 策略名称列表 (可选)
            save_path: 保存图表的路径 (可选)

        Returns:
            matplotlib图表对象
        """
        if names is None:
            names = [f"策略 {i+1}" for i in range(len(results_list))]

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # 绘制权益曲线
        for i, results in enumerate(results_list):
            ax1.plot(range(len(results['equity_curve'])), results['equity_curve'],
                    label=names[i], linewidth=2)

        ax1.set_title('权益曲线比较')
        ax1.set_ylabel('权益')
        ax1.legend()
        ax1.grid(True)

        # 创建性能指标表格
        metrics = ['total_return', 'annual_return', 'sharpe_ratio', 'max_drawdown',
                  'win_rate', 'profit_loss_ratio']
        metric_names = ['总收益率', '年化收益率', '夏普比率', '最大回撤', '胜率', '盈亏比']

        # 创建表格数据
        cell_text = []
        for results in results_list:
            row = []
            for metric in metrics:
                if metric in ['total_return', 'annual_return', 'max_drawdown', 'win_rate']:
                    row.append(f"{results[metric]:.2%}")
                else:
                    row.append(f"{results[metric]:.2f}")
            cell_text.append(row)

        # 绘制表格
        ax2.axis('tight')
        ax2.axis('off')
        table = ax2.table(cellText=cell_text, rowLabels=names, colLabels=metric_names,
                         loc='center', cellLoc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 1.5)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        if save_path:
            plt.savefig(save_path)
            print(f"比较图表已保存到 {save_path}")

        return fig

    def _create_signal_generator(self, signal_type):
        """
        创建信号生成器

        Args:
            signal_type: 信号生成器类型

        Returns:
            信号生成器实例
        """
        # 创建信号生成策略
        if signal_type == "threshold":
            return ThresholdSignalStrategy(threshold=0.6)
        elif signal_type == "topk":
            return TopKSignalStrategy(k=5, min_prob=0.3)
        elif signal_type == "momentum":
            return MomentumSignalStrategy(momentum_threshold=0.2)
        elif signal_type == "ensemble":
            # 创建集成策略
            threshold_strategy = ThresholdSignalStrategy(threshold=0.6)
            topk_strategy = TopKSignalStrategy(k=5, min_prob=0.3)
            momentum_strategy = MomentumSignalStrategy(momentum_threshold=0.2)

            return EnsembleSignalStrategy([
                (threshold_strategy, 0.5),
                (topk_strategy, 0.3),
                (momentum_strategy, 0.2)
            ])
        else:
            # 默认使用阈值策略
            return ThresholdSignalStrategy(threshold=0.6)
