import pandas as pd
import talib
import numpy as np
from pyqlab.const import MODEL_FUT_CODES
from pyqlab.utils.timefeatures import time_features, datetime_features
import torch

bar_set = []
def get_vocab():
    global bar_set  # 声明使用全局变量
    if len(bar_set) == 0:
        for i in range(-12, 13):
            for j in range(-12, 13):
                for k in range(0, 8):
                    for l in range(0, 8):
                        bar_set.append(f'{i}|{j}|{k}|{l}')
        # 添加特殊标记token
        bar_set.append('-88|0|0|0')  # 交易日间隔标记
        bar_set.append('-99|0|0|0')  # 假期(周末/节假日)间隔标记
    return bar_set

def idx2token(idx):
    global bar_set  # 声明使用全局变量
    if len(bar_set) == 0:
        bar_set = get_vocab()
    return bar_set[idx]

def to_time_tf(df, timeenc=0):
    # 将date列由timestamp转换为东8区日期时间
    df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)

    # 创建一个副本以保存原始的bar值，用于后续识别特殊标记
    # if 'bar' in df.columns:
    #     df['is_holiday'] = False
    #     # 获取特殊标记的索引
    #     if hasattr(Pipeline, 'bar_set'):
    #         holiday_bar_index = Pipeline.bar_set.index('-99|-99|0|0') if '-99|-99|0|0' in Pipeline.bar_set else -1
    #         if holiday_bar_index != -1:
    #             df.loc[df['bar'] == holiday_bar_index, 'is_holiday'] = True

    if timeenc == 0:
        # 使用更高效的向量化操作替代apply
        df['Month'] = df.datetime.dt.month - 1
        df['Day'] = df.datetime.dt.day - 1
        # 星期日设为0为一周的开端,weekday()返回0-6，对应星期一到星期日
        df['DayOfWeek'] = np.where(df.datetime.dt.weekday < 6, df.datetime.dt.weekday + 1, 0)
        df['Hour'] = df.datetime.dt.hour
        df['Minute'] = df.datetime.dt.minute // 5

        # 对于特殊标记（非交易日），设置特殊的时间特征
        if 'is_holiday' in df.columns:
            # 对于非交易日，设置特殊的时间特征值
            # 使用-1表示这是一个非交易日
            df.loc[df['is_holiday'], 'DayOfWeek'] = -1

            # 删除临时列
            df.drop(columns=['is_holiday'], inplace=True)

        # df = df.drop(['date'], axis=1).values
    elif timeenc == 1:
        df_stamp= time_features(pd.to_datetime(df['datetime'].values), freq='t').transpose(1, 0)
        df_tf = pd.DataFrame(df_stamp, columns=['MinuteOfHour', 'HourOfDay', 'DayOfWeek', 'DayOfMonth', 'DayOfYear',])
        df = pd.concat([df, df_tf], axis=1)

        # 对于特殊标记（非交易日），设置特殊的时间特征
        if 'is_holiday' in df.columns:
            # 对于非交易日，设置特殊的DayOfWeek值
            df.loc[df['is_holiday'], 'DayOfWeek'] = -1

            # 删除临时列
            df.drop(columns=['is_holiday'], inplace=True)
    elif timeenc == 2:
        df_stamp= datetime_features(pd.to_datetime(df['datetime'].values)) #.transpose(1, 0)
        # 只使用前5个时间特征以保持与模型的兼容性
        df_tf = pd.DataFrame(df_stamp[:, :5], columns=
                                ['hour_sin', 'hour_cos',
                            'dow_sin', 'dow_cos',
                            'month_sin'])
        df = pd.concat([df, df_tf], axis=1)
    else:
        raise ValueError("Invalid time encoding! timeenc should be 0 or 1.")
    return df

def candlestick_to_bar_token(code, df, pre_close, atr, timeenc=2, atr_mult=0.88, scale=10):
    """
    生成bar值的token数据，code，timestamp，bar token embedding index
    df columns: datetime, open, high, low, close
    """
    assert df.shape[1] >= 5, f"df shape is not 5: {df.shape}"
    assert pre_close > 0
    assert atr > 0
    atr *= atr_mult / scale

    bar_data = []
    for i in range(0, len(df)):  # 从1开始，因为需要计算与前一个收盘价的差值
        barenc = []

        barenc.append(code)
        # 日期 to timestamp
        barenc.append(int(df.iloc[i, 0].timestamp()))

        # 确保所有计算结果都在有效范围内
        if i == 0:
            change = np.clip(int((df.iloc[i, 4] - pre_close)/atr), -12, 12)
        else:
            change = np.clip(int((df.iloc[i, 4] - df.iloc[i-1, 4])/atr), -12, 12)
        entity = np.clip(int((df.iloc[i, 4] - df.iloc[i, 1])/atr), -12, 12)

        barenc.append(change)
        barenc.append(entity)

        if df.iloc[i, 4] > df.iloc[i, 1]: # 阳线
            upline = np.clip(int((df.iloc[i, 2] - df.iloc[i, 4])/atr), 0, 7)
            downline = np.clip(int((df.iloc[i, 1] - df.iloc[i, 3])/atr), 0, 7)
        else: # 阴线
            upline = np.clip(int((df.iloc[i, 2] - df.iloc[i, 1])/atr), 0, 7)
            downline = np.clip(int((df.iloc[i, 4] - df.iloc[i, 3])/atr), 0, 7)

        barenc.append(upline)
        barenc.append(downline)
        bar_data.append(barenc)

    df = pd.DataFrame(bar_data, columns=['code', 'datetime', 'change', 'entity', 'upline', 'downline'])
    fut_codes_dict = {code: i for i, code in enumerate(MODEL_FUT_CODES)}
    df['code'] = df['code'].apply(lambda x: fut_codes_dict[x])
    df = to_bar(df)
    df = to_time_tf(df, timeenc)
    return to_bar_token(df, 5 if timeenc == 2 else 5)

def to_bar_token(data, tf_len):
    code = data.iloc[:, 0].astype(int).values
    x = data.iloc[:, 2].astype(int).values
    x_mark = data.iloc[:, 3:3+tf_len].astype(float).values
    return code, x, x_mark

def to_bar(df):
    """
    生成bar值的token数据，code，timestamp，bar token embedding index
    """
    global bar_set  # 声明使用全局变量
    if len(bar_set) == 0:
        bar_set = get_vocab()

    # 将所有值限定在有效范围内
    df['change'] = df['change'].clip(-12, 12)  # 限定在 -12 到 12
    df['entity'] = df['entity'].clip(-12, 12)  # 限定在 -12 到 12
    df['upline'] = df['upline'].clip(0, 7)    # 限定在 0 到 7
    df['downline'] = df['downline'].clip(0, 7) # 限定在 0 到 7

    # 处理特殊标记token（-99表示非交易日间隔）
    holiday_mask = df['change'] == -99
    if holiday_mask.any():
        df.loc[holiday_mask, 'change'] = -99
        df.loc[holiday_mask, 'entity'] = 0
        df.loc[holiday_mask, 'upline'] = 0
        df.loc[holiday_mask, 'downline'] = 0

    # 生成bar字符串
    df.insert(2, 'bar', df['change'].astype(str) + '|' +
                       df['entity'].astype(str) + '|' +
                       df['upline'].astype(str) + '|' +
                       df['downline'].astype(str))

    # 验证所有生成的bar都在bar_set中
    invalid_bars = df[~df['bar'].isin(bar_set)]['bar'].unique()
    if len(invalid_bars) > 0:
        print(f"Warning: Found invalid bars: {invalid_bars}")
        # 将无效的bar替换为默认值 '0|0|0|0'
        df.loc[~df['bar'].isin(bar_set), 'bar'] = '0|0|0|0'

    print(f"bar_set size: {len(bar_set)}")
    df['bar'] = df['bar'].apply(lambda x: bar_set.index(x))
    df['bar'] = df['bar'].astype(int)
    df.drop(columns=['change', 'entity', 'upline', 'downline'], inplace=True)
    return df

def bar_token_to_candlestick(x, begin_date, begin_price, atr, freq='min5', atr_mult=0.88, scale=10):
    """
    将bar数据转换为蜡烛图数据

    Parameters:
    -----------
    x : array-like
        bar token序列
    begin_date : str or datetime
        起始日期
    begin_price : float
        起始价格
    atr : float
        ATR值
    """

    assert begin_price > 0
    assert atr > 0

    global bar_set  # 声明使用全局变量
    if len(bar_set) == 0:
        bar_set = get_vocab()

    # 确保begin_date是pandas的Timestamp类型
    if isinstance(begin_date, str):
        begin_date = pd.Timestamp(begin_date)
    elif isinstance(begin_date, np.datetime64):
        begin_date = pd.Timestamp(begin_date)
    elif not isinstance(begin_date, pd.Timestamp):
        begin_date = pd.Timestamp(begin_date)

    atr *= atr_mult / scale
    bars = []
    for i in range(len(x)):
        barenc = bar_set[x[i]]
        change, entity, upline, downline = barenc.split('|')
        change = int(change) * atr
        entity = int(entity) * atr
        upline = int(upline) * atr
        downline = int(downline) * atr

        if change == -99:  # 跳过假期标记
            continue
        if change == -88:  # 跳过交易日间隔标记
            continue

        close = begin_price + change
        open = close - entity
        if open < close:
            high = close + upline
            low = open - downline
        else:
            high = open + upline
            low = close - downline

        # 使用pandas的日期运算
        if freq == 'min5':
            current_date = begin_date + pd.Timedelta(minutes=5*i)
        else:
            current_date = begin_date + pd.Timedelta(days=i)
        bars.append([current_date, open, high, low, close])

        # 更新begin_price为当前close，用于下一个bar的计算
        begin_price = close

    # 创建DataFrame并设置正确的列名
    df = pd.DataFrame(bars, columns=['datetime', 'open', 'high', 'low', 'close'])
    return df


