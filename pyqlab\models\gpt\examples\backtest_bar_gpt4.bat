@echo off
e:
cd e:\lab\RoboQuant\pylab

@REM signal_type: threshold, topk, momentum, ensemble

python .\pyqlab\models\gpt\examples\backtest_bar_gpt4.py ^
--data_path f:/hqdata/fut_top_min1.parquet ^
--begin_date 2025-04-24 ^
--end_date 2025-12-31 ^
--model_path e:/lab/RoboQuant/pylab/model/FUT_GPT4_TOP_MIN1_30_16_8_050316_0.435_ls.onnx ^
--time_encoding timeF ^
--time_embed_type periodic ^
--pos_embed_type rope ^
--seq_len 30 ^
--commission 0.001 ^
--threshold 0.6 ^
--stop_loss 0.05 ^
--take_profit 0.1 ^
--temperature 0.8 ^
--signal_type threshold ^
--output_dir e:/lab/RoboQuant/pylab/results/bar_gpt4_backtest ^
--seed 42

@REM python .\pyqlab\models\gpt\examples\backtest_bar_gpt4.py ^
@REM --data_path f:/hqdata/fut_top_min1.parquet ^
@REM --begin_date 2025-04-20 ^
@REM --end_date 2025-12-31 ^
@REM --model_path e:/lab/RoboQuant/pylab/model/FUT_GPT4_TOP_MIN1_30_16_8_050316_0.435_ls.onnx ^
@REM --time_encoding timeF ^
@REM --time_embed_type periodic ^
@REM --pos_embed_type rope ^
@REM --seq_len 30 ^
@REM --commission 0.001 ^
@REM --threshold 0.6 ^
@REM --stop_loss 0.05 ^
@REM --take_profit 0.1 ^
@REM --temperature 0.8 ^
@REM --signal_type topk ^
@REM --output_dir e:/lab/RoboQuant/pylab/results/bar_gpt4_backtest ^
@REM --seed 42

@REM python .\pyqlab\models\gpt\examples\backtest_bar_gpt4.py ^
@REM --data_path f:/hqdata/fut_sf_min5.parquet ^
@REM --begin_date 2025-03-01 ^
@REM --end_date 2025-12-31 ^
@REM --model_path e:/lab/RoboQuant/pylab/model/FUT_GPT4_SF_MIN5_30_16_8_100908_0.297_ls.onnx ^
@REM --time_encoding timeF ^
@REM --time_embed_type periodic ^
@REM --pos_embed_type rope ^
@REM --seq_len 30 ^
@REM --commission 0.001 ^
@REM --threshold 0.6 ^
@REM --stop_loss 0.05 ^
@REM --take_profit 0.1 ^
@REM --temperature 0.8 ^
@REM --signal_type topk ^
@REM --output_dir e:/lab/RoboQuant/pylab/results/bar_gpt4_backtest ^
@REM --seed 42

